// Client-side subdomain detection plugin
// Created: 2025-07-13
// Purpose: Handle subdomain detection and routing on the client side

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()
  const router = useRouter()

  // Only run on client side
  if (process.server) return

  // Get current hostname
  const hostname = window.location.hostname
  const baseDomain = config.public.baseDomain || 'localhost:3000'
  
  // Extract subdomain from hostname
  const getSubdomain = (hostname: string): string | null => {
    // Reserved subdomains that should not be treated as schools
    const reservedSubdomains = ['www', 'api', 'admin', 'auth', 'mail', 'ftp', 'cdn', 'static']

    // Handle development environment
    if (hostname.includes('localhost')) {
      const parts = hostname.split('.')
      if (parts.length > 1 && parts[0] !== 'localhost' && !reservedSubdomains.includes(parts[0])) {
        return parts[0]
      }
      return null
    }
    
    // Handle production environment
    const baseParts = baseDomain.split('.')
    const hostParts = hostname.split('.')
    
    // If hostname has more parts than base domain, extract subdomain
    if (hostParts.length > baseParts.length) {
      const subdomainParts = hostParts.slice(0, hostParts.length - baseParts.length)
      return subdomainParts.join('.')
    }
    
    return null
  }

  // Detect current subdomain
  const currentSubdomain = getSubdomain(hostname)
  
  // Store subdomain in global state
  const subdomainState = useState('currentSubdomain', () => currentSubdomain)
  
  // Update subdomain state
  subdomainState.value = currentSubdomain
  
  // Handle subdomain routing
  if (currentSubdomain) {
    // Only log in development mode to reduce console noise
    if (process.dev) {
      console.log(`🏫 Detected school subdomain: ${currentSubdomain}`)
    }

    // Set school context
    const schoolContext = useState('schoolContext', () => ({
      schoolCode: currentSubdomain,
      isSchoolContext: true,
      detectedAt: new Date().toISOString()
    }))
    
    schoolContext.value = {
      schoolCode: currentSubdomain,
      isSchoolContext: true,
      detectedAt: new Date().toISOString()
    }
    
    // TEMPORARY: Disable client-side redirects for testing subdomain functionality
    // TODO: Re-enable after subdomain routing is working properly

    // Redirect to school-specific routes if needed
    const currentPath = router.currentRoute.value.path

    // If we're on the root path of a subdomain, redirect to school dashboard
    // if (currentPath === '/') {
    //   router.push(`/${currentSubdomain}`)
    // }

    // If we're on admin routes but in a school subdomain, redirect to main domain
    // if (currentPath.startsWith('/admin')) {
    //   const mainDomainUrl = `${window.location.protocol}//${baseDomain}${currentPath}`
    //   window.location.href = mainDomainUrl
    // }
    
  } else {
    // Only log in development mode to reduce console noise
    if (process.dev) {
      console.log('🌐 Main domain detected - no subdomain')
    }

    // Clear school context
    const schoolContext = useState('schoolContext', () => null)
    schoolContext.value = null
    
    // TEMPORARY: Disable main domain redirects for testing
    // TODO: Re-enable after subdomain routing is working properly

    // If we're trying to access school routes on main domain, redirect
    // const currentPath = router.currentRoute.value.path
    // const schoolRouteMatch = currentPath.match(/^\/([a-z0-9]+)\//)

    // if (schoolRouteMatch) {
    //   const schoolCode = schoolRouteMatch[1]
    //   // Don't redirect admin routes
    //   if (schoolCode !== 'admin') {
    //     const schoolDomainUrl = `${window.location.protocol}//${schoolCode}.${baseDomain}${currentPath}`
    //     window.location.href = schoolDomainUrl
    //   }
    // }
  }

  // Provide subdomain utilities
  return {
    provide: {
      subdomain: {
        current: currentSubdomain,
        isSchoolContext: !!currentSubdomain,
        getSchoolUrl: (schoolCode: string, path: string = '/') => {
          return `${window.location.protocol}//${schoolCode}.${baseDomain}${path}`
        },
        getMainUrl: (path: string = '/') => {
          return `${window.location.protocol}//${baseDomain}${path}`
        },
        switchToSchool: (schoolCode: string, path: string = '/') => {
          const url = `${window.location.protocol}//${schoolCode}.${baseDomain}${path}`
          window.location.href = url
        },
        switchToMain: (path: string = '/') => {
          const url = `${window.location.protocol}//${baseDomain}${path}`
          window.location.href = url
        }
      }
    }
  }
})
