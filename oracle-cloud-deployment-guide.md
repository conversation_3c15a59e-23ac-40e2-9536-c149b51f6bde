# Oracle Cloud Always Free Deployment Guide for Nuxt.js Multi-Tenant App

## Overview
This guide provides step-by-step instructions to deploy your Nuxt.js multi-tenant application on Oracle Cloud's Always Free tier with subdomain support and Stripe integration.

## Prerequisites
- Oracle Cloud account (Always Free tier)
- Domain name with DNS management access
- Nuxt.js application ready for production
- Stripe account with API keys

## Step 1: Set Up Oracle Cloud Infrastructure

### 1.1 Create Always Free Compute Instance
1. Log into Oracle Cloud Console
2. Navigate to **Compute > Instances**
3. Click **Create Instance**
4. Configure:
   - **Name**: `nuxt-app-server`
   - **Image**: Ubuntu 22.04 LTS
   - **Shape**: VM.Standard.E2.1.Micro (Always Free)
   - **Boot Volume**: 50GB (Always Free)
   - **SSH Keys**: Add your public key

### 1.2 Configure Security Rules
1. Go to **Networking > Virtual Cloud Networks**
2. Select your VCN (or create new one)
3. Add Ingress Rules:
   ```
   TCP 22    (SSH)
   TCP 80    (HTTP)
   TCP 443   (HTTPS)
   TCP 3000  (Nuxt.js dev)
   ```

## Step 2: Connect to Your Instance

```bash
# Connect via SSH
ssh -i ~/.ssh/your-key.pem ubuntu@your-instance-ip

# Update system
sudo apt update && sudo apt upgrade -y
```

## Step 3: Install Required Software

```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install nginx
sudo apt install nginx -y

# Install PM2 for process management
sudo npm install -g pm2

# Install certbot for SSL
sudo apt install certbot python3-certbot-nginx -y
```

## Step 4: Configure Nginx for Multi-Tenant Subdomains

### 4.1 Create Nginx Configuration
```bash
sudo nano /etc/nginx/sites-available/nuxt-app
```

Add the following configuration:
```nginx
# /etc/nginx/sites-available/nuxt-app
server {
    listen 80;
    server_name *.yourdomain.com yourdomain.com;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Handle Stripe webhooks
    location /api/webhooks/stripe {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Disable buffering for webhooks
        proxy_request_buffering off;
    }
    
    # Main application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 4.2 Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/nuxt-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Step 5: Deploy Your Nuxt.js Application

### 5.1 Clone Your Repository
```bash
cd /var/www
sudo git clone https://github.com/yourusername/your-nuxt-app.git
cd your-nuxt-app
sudo chown -R ubuntu:ubuntu .
```

### 5.2 Install Dependencies and Build
```bash
# Install dependencies
npm install

# Build for production
npm run build
```

### 5.3 Configure Environment Variables
```bash
# Create .env file
nano .env

# Add your environment variables
NUXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
DATABASE_URL=your_database_url
NUXT_PUBLIC_APP_URL=https://yourdomain.com
```

### 5.4 Create PM2 Ecosystem File
```bash
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'nuxt-app',
    port: 3000,
    exec_mode: 'cluster',
    instances: 'max',
    script: './.output/server/index.mjs',
    env: {
      NODE_ENV: 'production'
    }
  }]
}
```

### 5.5 Start Application with PM2
```bash
# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup startup script
pm2 startup
sudo env PATH=$PATH:/usr/bin pm2 startup systemd -u ubuntu --hp /home/<USER>
```

## Step 6: Configure SSL Certificates

### 6.1 Obtain SSL Certificate
```bash
# Get SSL certificate for main domain and wildcard
sudo certbot --nginx -d yourdomain.com -d *.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## Step 7: Configure DNS for Subdomains

### 7.1 DNS Configuration
In your domain registrar's DNS settings, add:
```
A     *     your-instance-ip
A     @     your-instance-ip
```

### 7.2 Verify Subdomain Routing
Your Nuxt.js app should handle subdomain routing. Example middleware:

```javascript
// middleware/subdomain.js
export default defineNuxtRouteMiddleware((to) => {
  const host = useRequestHeaders().host || ''
  const subdomain = host.split('.')[0]
  
  if (subdomain && subdomain !== 'www' && subdomain !== 'yourdomain') {
    // Set tenant based on subdomain
    useState('tenant', () => subdomain)
  }
})
```

## Step 8: Stripe Webhook Configuration

### 8.1 Configure Stripe Webhook Endpoint
1. Go to Stripe Dashboard > Developers > Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
3. Select events: `payment_intent.succeeded`, `invoice.payment_succeeded`, etc.

### 8.2 Test Webhook
```bash
# Install Stripe CLI for testing
curl -s https://packages.stripe.dev/api/security/keypair/stripe-cli-gpg/public/key.gpg | sudo gpg --dearmor -o /usr/share/keyrings/stripe.gpg
echo "deb [signed-by=/usr/share/keyrings/stripe.gpg] https://packages.stripe.dev/stripe-cli-debian-local stable main" | sudo tee /etc/apt/sources.list.d/stripe-cli.list
sudo apt update
sudo apt install stripe

# Login and test webhook
stripe login
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```

## Step 9: Database Setup (PostgreSQL)

### 9.1 Install PostgreSQL
```bash
sudo apt install postgresql postgresql-contrib -y
sudo -u postgres createuser ubuntu
sudo -u postgres createdb nuxtapp
sudo -u postgres psql
```

```sql
-- In PostgreSQL
ALTER USER ubuntu WITH PASSWORD 'yourpassword';
GRANT ALL PRIVILEGES ON DATABASE nuxtapp TO ubuntu;
```

## Step 10: Monitoring and Maintenance

### 10.1 Set Up Log Rotation
```bash
sudo nano /etc/logrotate.d/pm2-ubuntu
```

```
/home/<USER>/.pm2/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0640 ubuntu ubuntu
    sharedscripts
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 10.2 Create Backup Script
```bash
nano ~/backup.sh
```

```bash
#!/bin/bash
# Database backup
pg_dump nuxtapp > backup_$(date +%Y%m%d_%H%M%S).sql

# Application backup
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/your-nuxt-app
```

## Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   ```bash
   pm2 logs
   sudo nginx -t
   sudo systemctl restart nginx
   ```

2. **Subdomain not working**
   - Check DNS propagation: `dig subdomain.yourdomain.com`
   - Verify nginx configuration

3. **Stripe webhook not receiving**
   - Check webhook URL in Stripe dashboard
   - Verify SSL certificate is valid
   - Check nginx configuration for webhook endpoint

## Performance Optimization

### Enable HTTP/2
```bash
sudo nano /etc/nginx/sites-available/nuxt-app
```
Add `http2` to listen directive:
```nginx
listen 443 ssl http2;
```

### Enable Brotli Compression
```bash
sudo apt install nginx-full -y
sudo nano /etc/nginx/nginx.conf
```
Add:
```nginx
load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;

http {
    brotli on;
    brotli_comp_level 6;
    brotli_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

## Cost Analysis
- **Oracle Cloud Always Free**: $0/month
  - 1 OCPU, 1GB RAM
  - 50GB storage
  - 10TB outbound traffic/month
- **Domain**: ~$10-15/year
- **Total Monthly Cost**: $0 (excluding domain)

## Next Steps
1. Monitor application performance
2. Set up monitoring alerts
3. Configure automated backups
4. Review security settings regularly