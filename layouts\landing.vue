<template>
  <div class="min-h-screen bg-white dark:bg-gray-900">
    <!-- Navigation Header -->
    <header class="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <NuxtLink to="/" class="flex items-center space-x-2">
              <Icon name="heroicons:academic-cap" class="h-8 w-8 text-blue-600" />
              <span class="text-xl font-bold text-gray-900 dark:text-white">RPHMate</span>
            </NuxtLink>
          </div>

          <!-- Navigation Links -->
          <div class="hidden md:flex items-center space-x-8">
            <NuxtLink to="/"
              class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Home
            </NuxtLink>
            <NuxtLink to="/pricing"
              class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Pricing
            </NuxtLink>
            <NuxtLink to="/features"
              class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Features
            </NuxtLink>
            <NuxtLink to="/contact"
              class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Contact
            </NuxtLink>
          </div>

          <!-- Auth Buttons -->
          <div class="hidden md:flex items-center space-x-4">
            <NuxtLink to="/login"
              class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Sign In
            </NuxtLink>
            <NuxtLink to="/pricing"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
              Get Started
            </NuxtLink>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button @click="toggleMobileMenu"
              class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
              <Icon name="heroicons:bars-3" class="h-6 w-6" />
            </button>
          </div>
        </div>

        <!-- Mobile Navigation Menu - Optimized with ClientOnly -->
        <ClientOnly>
          <Transition enter-active-class="transition duration-200 ease-out"
            enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-100 ease-in" leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0">
            <div v-if="isMobileMenuOpen && isMobileViewport"
              class="md:hidden border-t border-gray-200 dark:border-gray-700 py-4">
              <div class="flex flex-col space-y-4">
                <NuxtLink to="/"
                  class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  @click="closeMobileMenu">
                  Home
                </NuxtLink>
                <NuxtLink to="/pricing"
                  class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  @click="closeMobileMenu">
                  Pricing
                </NuxtLink>
                <NuxtLink to="/features"
                  class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  @click="closeMobileMenu">
                  Features
                </NuxtLink>
                <NuxtLink to="/contact"
                  class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  @click="closeMobileMenu">
                  Contact
                </NuxtLink>
                <div class="border-t border-gray-200 dark:border-gray-700 pt-4 flex flex-col space-y-2">
                  <NuxtLink to="/admin/login"
                    class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    @click="closeMobileMenu">
                    Sign In
                  </NuxtLink>
                  <NuxtLink to="/admin/register"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-center"
                    @click="closeMobileMenu">
                    Get Started
                  </NuxtLink>
                </div>
              </div>
            </div>
          </Transition>
        </ClientOnly>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-2 mb-4">
              <Icon name="heroicons:academic-cap" class="h-8 w-8 text-blue-600" />
              <span class="text-xl font-bold text-gray-900 dark:text-white">RPHMate</span>
            </div>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              Streamline your teaching journey with our comprehensive educational platform.
              Manage classes, track progress, and enhance your teaching experience.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                <Icon name="heroicons:envelope" class="h-5 w-5" />
              </a>
              <a href="#" class="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                <Icon name="heroicons:phone" class="h-5 w-5" />
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
              Product
            </h3>
            <ul class="space-y-2">
              <li>
                <NuxtLink to="/features"
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">Features
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/pricing"
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">Pricing
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/demo"
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">Demo</NuxtLink>
              </li>
            </ul>
          </div>

          <!-- Support -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
              Support
            </h3>
            <ul class="space-y-2">
              <li>
                <NuxtLink to="/help"
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">Help Center
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/contact"
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">Contact Us
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/privacy"
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">Privacy Policy
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/terms"
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">Terms of Service
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>

        <!-- Bottom Footer -->
        <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              © {{ new Date().getFullYear() }} RPHMate. All rights reserved.
            </p>
            <div class="flex items-center space-x-4 mt-4 md:mt-0">
              <!-- Theme Switcher -->
              <UiBaseThemeSwitcher />
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Toast Container -->
    <ClientOnly>
      <UiToastContainer />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// Mobile menu state
const isMobileMenuOpen = ref(false)
const isMobileViewport = ref(false)

// Check if viewport is mobile size
const checkViewport = () => {
  isMobileViewport.value = window.innerWidth < 768
}

// Mobile menu toggle functions
const toggleMobileMenu = () => {
  if (isMobileViewport.value) {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
  }
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// Handle escape key to close mobile menu
const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isMobileMenuOpen.value) {
    closeMobileMenu()
  }
}

// Handle click outside to close mobile menu
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const mobileMenu = document.querySelector('.mobile-menu-container')
  const menuButton = document.querySelector('.mobile-menu-button')

  if (mobileMenu && !mobileMenu.contains(target) && menuButton && !menuButton.contains(target)) {
    closeMobileMenu()
  }
}

// Setup and cleanup
onMounted(() => {
  // Initial viewport check
  checkViewport()

  // Add event listeners
  window.addEventListener('resize', checkViewport)
  window.addEventListener('keydown', handleEscapeKey)
  document.addEventListener('click', handleClickOutside)

  // Close menu on route change
  const router = useRouter()
  router.afterEach(() => {
    closeMobileMenu()
  })
})

onUnmounted(() => {
  // Cleanup event listeners
  window.removeEventListener('resize', checkViewport)
  window.removeEventListener('keydown', handleEscapeKey)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* Custom styles for landing layout */
</style>
